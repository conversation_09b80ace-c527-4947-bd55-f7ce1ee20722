# Dependencies
node_modules
.pnp
.pnp.js

# Testing
coverage

# Production
build
dist
dist-ssr
*.local

# Misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# TypeScript
*.tsbuildinfo
m a i n p u s h / 
 
 m a i n p u s h / 
 
 m a i n p u s h / 
 
 
.vercel
.env*
!.env.example
.venv
