import ReactGA from "react-ga4";

export const initGA = (measurementId: string) => {
  if (typeof window !== 'undefined') {
    // Defer Google Analytics loading until after page load to avoid blocking
    const loadGA = () => {
      // Load the Google Analytics script
      const script = document.createElement('script');
      script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
      script.async = true;
      document.head.appendChild(script);

      // Initialize gtag
      (window as any).dataLayer = (window as any).dataLayer || [];
      function gtag(...args: any[]) {
        (window as any).dataLayer.push(args);
      }
      (window as any).gtag = gtag;
      gtag('js', new Date());
      gtag('config', measurementId);
    };

    // Defer loading until after page load event
    window.addEventListener('load', () => {
      if ('requestIdleCallback' in window) {
        requestIdleCallback(loadGA);
      } else {
        setTimeout(loadGA, 100);
      }
    });
  }
};

export const logPageView = (path: string) => {
  ReactGA.send({ hitType: "pageview", page: path });
};

export const logEvent = (category: string, action: string, label?: string) => {
  ReactGA.event({
    category,
    action,
    label
  });
};

// Google Analytics event tracking utility
export const trackEvent = (
  category: string,
  action: string,
  label?: string,
  value?: number
) => {
  // Check if gtag is available
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value
    });
  }
};