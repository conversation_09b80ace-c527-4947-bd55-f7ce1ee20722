# 🏃 De Koninklijke Loop 2026 (DKL)

Een moderne, hoogperformante webapplicatie voor De Koninklijke Loop 2026 - een uniek wandelevenement waar mensen met een beperking lopen voor het goede doel.

---

## 🚀 Quick Start

### Installatie

```bash
# Clone repository
git clone https://github.com/Jeffreasy/DKL25.git
cd DKL25

# Installeer dependencies
npm install

# Start development server
npm run dev
```

### Environment Setup

Maak een `.env` bestand aan:

```env
# Supabase
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Email Service
VITE_EMAIL_SERVICE_URL=https://dklemailservice.onrender.com

# Analytics
VITE_GA_MEASUREMENT_ID=your_ga_measurement_id
```

---

## 📚 Documentatie

**Complete documentatie:** [`docs/`](docs/README.md)

### Quick Links

- 🏗️ **Architectuur:** [`docs/architecture/`](docs/architecture/PROJECT_OVERVIEW.md)
- 📚 **Development Guides:** [`docs/guides/`](docs/guides/REFACTORING_GUIDE.md)
- ⚡ **Performance:** [`docs/performance/`](docs/performance/PERFORMANCE_GUIDE.md)
- 🎨 **Design System:** [`docs/styling/`](docs/styling/DESIGN_SYSTEM.md)
- 🔍 **SEO:** [`docs/seo/`](docs/seo/SEO_COMPLETE_GUIDE.md)
- 🔧 **Technical:** [`docs/technical/`](docs/technical/IMPLEMENTATION_NOTES.md)

---

## 💻 Tech Stack

**Frontend:**
- React 18 + TypeScript
- Vite + Tailwind CSS
- React Router + React Hook Form
- Framer Motion + Material-UI Icons

**Backend:**
- Go 1.21+ + PostgreSQL
- Supabase + Cloudinary
- Email Service (SMTP)

---

## ✨ Features

- ✅ Modern responsive design
- ✅ SEO optimized (27+ schemas)
- ✅ WCAG 2.1 AA accessible
- ✅ 90+ Lighthouse score
- ✅ Real-time analytics
- ✅ AI chatbot (61 FAQ)
- ✅ Online registration
- ✅ Media galleries
- ✅ Email automation

---

## 🎯 Performance

- **Bundle Size:** ~414.5 kB
- **Lighthouse:** 90+ score
- **Load Time:** <2s TTI
- **CLS:** <0.15

Zie [`docs/performance/`](docs/performance/PERFORMANCE_GUIDE.md) voor details.

---

## 📦 Scripts

```bash
npm run dev          # Development server
npm run build        # Production build
npm run preview      # Preview build
npm run lint         # Run linter
npm run type-check   # TypeScript check
```

---

## 📞 Contact

- **Website:** https://dekoninklijkeloop.nl
- **Email:** <EMAIL>
- **GitHub:** https://github.com/Jeffreasy/DKL25

## 📄 Licentie

Dit project is eigendom van De Koninklijke Loop. Alle rechten voorbehouden.

## 👥 Bijdragen

Voor bijdragen, neem contact op met het development team via het contactformulier op de website.

## 📞 Contact

Voor vragen of ondersteuning:
- Email: <EMAIL>
- Website: https://dekoninklijkeloop.nl