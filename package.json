{"name": "dkl25<PERSON>in", "version": "1.0.0", "private": true, "type": "module", "description": "De Koninklijke Loop 2026 - Event Website", "author": "DKL Team", "license": "UNLICENSED", "engines": {"node": "22.x", "npm": ">=9.0.0"}, "scripts": {"dev": "vite", "build": "tsc --noEmit && vite build", "build:client": "vite build", "type-check": "tsc --noEmit", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "clean": "rm -rf dist node_modules/.vite", "api": "ts-node server.ts"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.14.1", "@headlessui/react": "^2.2.9", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.2", "@mui/icons-material": "^7.3.4", "@mui/material": "^7.3.4", "@supabase/supabase-js": "^2.75.0", "@vercel/analytics": "^1.4.1", "@vercel/speed-insights": "^1.2.0", "focus-trap-react": "^11.0.4", "form-data": "^4.0.1", "framer-motion": "^12.23.22", "juice": "^11.0.3", "mailgun.js": "^12.1.0", "node-fetch": "^3.3.2", "nodemailer": "^7.0.9", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-ga4": "^2.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.64.0", "react-hot-toast": "^2.6.0", "react-router-dom": "^7.9.4", "react-use": "^17.6.0", "resend": "^6.1.2", "tailwind-scrollbar-hide": "^4.0.0", "uuid": "^13.0.0", "zod": "^4.1.12"}, "devDependencies": {"@eslint/js": "^9.37.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.19", "@types/cors": "^2.8.19", "@types/dompurify": "^3.0.5", "@types/express": "^5.0.3", "@types/lodash.debounce": "^4.0.9", "@types/node": "^24.7.1", "@types/nodemailer": "^7.0.2", "@types/qrcode": "^1.5.5", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.46.0", "@typescript-eslint/parser": "^8.46.0", "@vercel/node": "^5.3.26", "@vitejs/plugin-react": "^5.0.4", "autoprefixer": "^10.4.16", "concurrently": "^9.2.1", "cors": "^2.8.5", "dotenv": "^17.2.3", "esbuild": "^0.25.10", "eslint": "^9.37.0", "eslint-plugin-react-hooks": "^7.0.0", "eslint-plugin-react-refresh": "^0.4.23", "express": "^5.1.0", "globby": "^15.0.0", "postcss": "^8.4.32", "prettier": "^3.6.2", "rollup-plugin-visualizer": "^6.0.4", "supabase": "^2.48.3", "tailwindcss": "^3.4.0", "ts-node": "^10.9.1", "tsx": "^4.20.6", "typescript": "^5.9.3", "vite": "^7.1.9", "vite-plugin-pwa": "^1.0.3"}}