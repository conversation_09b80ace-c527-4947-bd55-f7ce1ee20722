{"version": 2, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,HEAD,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type,Range"}]}, {"source": "/(index\\.html)?", "headers": [{"key": "Cache-Control", "value": "no-cache"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/assets/(.*)\\.js", "headers": [{"key": "Content-Type", "value": "application/javascript"}]}, {"source": "/assets/(.*)\\.mjs", "headers": [{"key": "Content-Type", "value": "text/javascript"}]}, {"source": "/(.*)\\.js", "headers": [{"key": "Content-Type", "value": "application/javascript"}]}, {"source": "/(.*)\\.wasm", "headers": [{"key": "Content-Type", "value": "application/wasm"}]}, {"source": "/fonts/(.*)\\.woff2", "headers": [{"key": "Content-Type", "value": "font/woff2"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}], "rewrites": [{"source": "/api/:path*", "destination": "https://dklemailservice.onrender.com/api/:path*"}, {"source": "/(.*)", "destination": "/index.html"}]}